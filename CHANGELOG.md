# Changelog

All notable changes to BlenderGPT will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.1.0] - 2024-01-XX

### 🚀 Added
- **Multi-Provider Support**: OpenAI, Anthropic Claude, Ollama, and Custom APIs
- **Security System**: Code validation and preview before execution
- **Context Awareness**: AI understands current scene, selection, and mode
- **Quick Actions**: Pre-defined buttons for common operations
- **Enhanced UI**: Modern interface with better organization and icons
- **Safe Execution Environment**: Restricted code execution with limited permissions
- **Provider-Specific Settings**: Custom base URLs for Ollama and custom APIs
- **Model Refresh**: Dynamic model loading for each provider
- **Better Error Handling**: More informative error messages and recovery
- **Chat History Improvements**: Timestamps, approval status, and better display

### 🔒 Security
- **Code Validation**: AST-based analysis of generated code
- **Dangerous Function Detection**: Blocks potentially harmful operations
- **File System Protection**: Prevents unauthorized file access
- **Network Protection**: Blocks network operations in generated code
- **Safe Globals**: Restricted execution environment with limited built-ins

### 🎨 UI/UX Improvements
- **Provider Selection**: Easy switching between AI providers
- **Security Settings**: Toggle security validation and code preview
- **Quick Actions**: One-click common operations
- **Better Chat Display**: Improved message formatting and icons
- **Progress Indicators**: Visual feedback during AI processing
- **Enhanced Preferences**: Organized settings with helpful links

### 🔧 Technical Improvements
- **Modern API Integration**: Updated from deprecated OpenAI v0.27.2
- **Async Support**: Better handling of streaming responses
- **Error Recovery**: Graceful fallback mechanisms
- **Code Organization**: Modular architecture with separate security and API modules
- **Type Hints**: Better code documentation and IDE support

### 🐛 Fixed
- **Typo Fix**: Corrected `unegister_class` to `unregister_class`
- **Memory Leaks**: Proper cleanup of properties on addon disable
- **API Compatibility**: Updated to work with modern API versions
- **Error Handling**: Better exception catching and user feedback

### 📚 Documentation
- **Comprehensive README**: Detailed setup and usage instructions
- **Security Guide**: Best practices for safe usage
- **Provider Setup**: Step-by-step configuration for each AI provider
- **Troubleshooting**: Common issues and solutions
- **API Documentation**: Developer guide for extending the addon

### ⚠️ Breaking Changes
- **Minimum Blender Version**: Now requires Blender 3.1+
- **API Key Storage**: Separate fields for different providers
- **Property Names**: Some internal property names changed for consistency

### 🔄 Migration Guide
If upgrading from v2.0.0:
1. Re-enter your API keys in the new preferences layout
2. Select your preferred AI provider
3. Update any custom scripts that reference old property names
4. Enable security validation for safer operation

---

## [2.0.0] - 2023-XX-XX (Original Release)

### Added
- Basic OpenAI GPT-4/GPT-3.5 integration
- Simple chat interface
- Code generation and execution
- Basic error handling
- Blender UI integration

### Features
- Natural language to Blender Python code
- Chat history
- Model selection (GPT-4, GPT-3.5 Turbo)
- Direct code execution
- Text editor integration

---

## Planned Features (Future Releases)

### [2.2.0] - Context & Workflow
- **Scene Analysis**: Detailed scene understanding
- **Batch Operations**: Multi-object operations
- **Workflow Memory**: Remember user preferences and patterns
- **Macro System**: Save and replay command sequences
- **Template Library**: Pre-built command templates

### [2.3.0] - Advanced AI
- **Image-to-3D**: Generate models from images
- **Style Transfer**: Apply artistic styles to objects
- **Animation AI**: Intelligent animation generation
- **Procedural Generation**: Advanced content creation

### [2.4.0] - Collaboration
- **Team Features**: Shared templates and macros
- **Cloud Sync**: Synchronize settings across devices
- **Version Control**: Git integration for project management
- **Asset Management**: Intelligent 3D asset organization

### [3.0.0] - Next Generation
- **Multimodal AI**: Voice, image, and text input
- **VR/AR Support**: Spatial computing integration
- **Real-time Collaboration**: Live multi-user sessions
- **Enterprise Features**: Advanced security and management

---

## Support

For questions, issues, or feature requests:
- **GitHub Issues**: [Report bugs or request features](https://github.com/gd3kr/BlenderGPT/issues)
- **Discussions**: [Community discussions](https://github.com/gd3kr/BlenderGPT/discussions)
- **Wiki**: [Documentation and guides](https://github.com/gd3kr/BlenderGPT/wiki)

---

## Contributors

Thanks to all contributors who have helped improve BlenderGPT:
- [@gd3kr](https://github.com/gd3kr) - Original creator
- Community contributors and testers
- AI providers: OpenAI, Anthropic, Ollama team

---

**Note**: This changelog follows [semantic versioning](https://semver.org/). 
- **Major** versions include breaking changes
- **Minor** versions add new features
- **Patch** versions include bug fixes and improvements
