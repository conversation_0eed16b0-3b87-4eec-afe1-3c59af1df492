"""
BlenderGPT API Providers Module
Supports multiple LLM providers with OpenAI-compatible APIs
"""

import json
import requests
import asyncio
import aiohttp
from typing import Dict, List, Optional, Iterator, Any, Tuple
from abc import ABC, abstractmethod
from enum import Enum

class APIProvider(Enum):
    """Supported API providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    COHERE = "cohere"
    HUGGINGFACE = "huggingface"
    OLLAMA = "ollama"
    CUSTOM = "custom"

class LLMProvider(ABC):
    """Abstract base class for LLM providers"""
    
    def __init__(self, api_key: str, base_url: str, model: str):
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.timeout = 30
        
    @abstractmethod
    def create_chat_completion(self, messages: List[Dict], **kwargs) -> Dict:
        """Create a chat completion"""
        pass
    
    @abstractmethod
    def create_chat_completion_stream(self, messages: List[Dict], **kwargs) -> Iterator[Dict]:
        """Create a streaming chat completion"""
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        pass

class OpenAIProvider(LLMProvider):
    """OpenAI API provider"""
    
    def __init__(self, api_key: str, model: str = "gpt-4"):
        super().__init__(api_key, "https://api.openai.com/v1", model)
    
    def create_chat_completion(self, messages: List[Dict], **kwargs) -> Dict:
        """Create OpenAI chat completion"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": messages,
            **kwargs
        }
        
        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=self.timeout
        )
        
        if response.status_code != 200:
            raise Exception(f"OpenAI API error: {response.status_code} - {response.text}")
        
        return response.json()
    
    def create_chat_completion_stream(self, messages: List[Dict], **kwargs) -> Iterator[Dict]:
        """Create OpenAI streaming chat completion"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": messages,
            "stream": True,
            **kwargs
        }
        
        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=self.timeout,
            stream=True
        )
        
        if response.status_code != 200:
            raise Exception(f"OpenAI API error: {response.status_code}")
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data = line[6:]
                    if data.strip() == '[DONE]':
                        break
                    try:
                        yield json.loads(data)
                    except json.JSONDecodeError:
                        continue
    
    def get_available_models(self) -> List[str]:
        """Get OpenAI available models"""
        return ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-16k"]

class AnthropicProvider(LLMProvider):
    """Anthropic Claude API provider"""
    
    def __init__(self, api_key: str, model: str = "claude-3-sonnet-20240229"):
        super().__init__(api_key, "https://api.anthropic.com/v1", model)
    
    def create_chat_completion(self, messages: List[Dict], **kwargs) -> Dict:
        """Create Anthropic chat completion"""
        headers = {
            "x-api-key": self.api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        
        # Convert OpenAI format to Anthropic format
        anthropic_messages = self._convert_messages_to_anthropic(messages)
        
        data = {
            "model": self.model,
            "messages": anthropic_messages,
            "max_tokens": kwargs.get("max_tokens", 1500),
            **{k: v for k, v in kwargs.items() if k != "max_tokens"}
        }
        
        response = requests.post(
            f"{self.base_url}/messages",
            headers=headers,
            json=data,
            timeout=self.timeout
        )
        
        if response.status_code != 200:
            raise Exception(f"Anthropic API error: {response.status_code} - {response.text}")
        
        # Convert response to OpenAI format
        anthropic_response = response.json()
        return self._convert_anthropic_response(anthropic_response)
    
    def create_chat_completion_stream(self, messages: List[Dict], **kwargs) -> Iterator[Dict]:
        """Create Anthropic streaming chat completion"""
        # Anthropic streaming implementation
        # For now, fall back to non-streaming
        response = self.create_chat_completion(messages, **kwargs)
        yield response
    
    def get_available_models(self) -> List[str]:
        """Get Anthropic available models"""
        return ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"]
    
    def _convert_messages_to_anthropic(self, messages: List[Dict]) -> List[Dict]:
        """Convert OpenAI message format to Anthropic format"""
        anthropic_messages = []
        for msg in messages:
            if msg["role"] == "system":
                # Anthropic handles system messages differently
                continue
            anthropic_messages.append({
                "role": msg["role"],
                "content": msg["content"]
            })
        return anthropic_messages
    
    def _convert_anthropic_response(self, response: Dict) -> Dict:
        """Convert Anthropic response to OpenAI format"""
        return {
            "choices": [{
                "message": {
                    "role": "assistant",
                    "content": response["content"][0]["text"]
                },
                "finish_reason": "stop"
            }],
            "usage": response.get("usage", {})
        }

class OllamaProvider(LLMProvider):
    """Ollama local API provider"""
    
    def __init__(self, api_key: str = "", base_url: str = "http://localhost:11434", model: str = "llama2"):
        super().__init__(api_key, base_url, model)
    
    def create_chat_completion(self, messages: List[Dict], **kwargs) -> Dict:
        """Create Ollama chat completion"""
        data = {
            "model": self.model,
            "messages": messages,
            "stream": False,
            **kwargs
        }
        
        response = requests.post(
            f"{self.base_url}/api/chat",
            json=data,
            timeout=self.timeout
        )
        
        if response.status_code != 200:
            raise Exception(f"Ollama API error: {response.status_code} - {response.text}")
        
        ollama_response = response.json()
        return self._convert_ollama_response(ollama_response)
    
    def create_chat_completion_stream(self, messages: List[Dict], **kwargs) -> Iterator[Dict]:
        """Create Ollama streaming chat completion"""
        data = {
            "model": self.model,
            "messages": messages,
            "stream": True,
            **kwargs
        }
        
        response = requests.post(
            f"{self.base_url}/api/chat",
            json=data,
            timeout=self.timeout,
            stream=True
        )
        
        if response.status_code != 200:
            raise Exception(f"Ollama API error: {response.status_code}")
        
        for line in response.iter_lines():
            if line:
                try:
                    data = json.loads(line.decode('utf-8'))
                    yield self._convert_ollama_stream_response(data)
                except json.JSONDecodeError:
                    continue
    
    def get_available_models(self) -> List[str]:
        """Get Ollama available models"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get("models", [])
                return [model["name"] for model in models]
        except:
            pass
        return ["llama2", "codellama", "mistral"]
    
    def _convert_ollama_response(self, response: Dict) -> Dict:
        """Convert Ollama response to OpenAI format"""
        return {
            "choices": [{
                "message": {
                    "role": "assistant",
                    "content": response.get("message", {}).get("content", "")
                },
                "finish_reason": "stop"
            }]
        }
    
    def _convert_ollama_stream_response(self, response: Dict) -> Dict:
        """Convert Ollama stream response to OpenAI format"""
        return {
            "choices": [{
                "delta": {
                    "content": response.get("message", {}).get("content", "")
                },
                "finish_reason": "stop" if response.get("done", False) else None
            }]
        }

class CustomProvider(LLMProvider):
    """Custom OpenAI-compatible API provider"""
    
    def __init__(self, api_key: str, base_url: str, model: str):
        super().__init__(api_key, base_url, model)
    
    def create_chat_completion(self, messages: List[Dict], **kwargs) -> Dict:
        """Create custom API chat completion"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": messages,
            **kwargs
        }
        
        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=self.timeout
        )
        
        if response.status_code != 200:
            raise Exception(f"Custom API error: {response.status_code} - {response.text}")
        
        return response.json()
    
    def create_chat_completion_stream(self, messages: List[Dict], **kwargs) -> Iterator[Dict]:
        """Create custom API streaming chat completion"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": messages,
            "stream": True,
            **kwargs
        }
        
        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=self.timeout,
            stream=True
        )
        
        if response.status_code != 200:
            raise Exception(f"Custom API error: {response.status_code}")
        
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data = line[6:]
                    if data.strip() == '[DONE]':
                        break
                    try:
                        yield json.loads(data)
                    except json.JSONDecodeError:
                        continue
    
    def get_available_models(self) -> List[str]:
        """Get custom API available models"""
        try:
            headers = {"Authorization": f"Bearer {self.api_key}"}
            response = requests.get(f"{self.base_url}/models", headers=headers, timeout=5)
            if response.status_code == 200:
                models = response.json().get("data", [])
                return [model["id"] for model in models]
        except:
            pass
        return [self.model]

class APIProviderManager:
    """Manager for different API providers"""
    
    def __init__(self):
        self.providers = {}
    
    def register_provider(self, provider_type: APIProvider, provider: LLMProvider):
        """Register a new provider"""
        self.providers[provider_type] = provider
    
    def get_provider(self, provider_type: APIProvider) -> Optional[LLMProvider]:
        """Get a registered provider"""
        return self.providers.get(provider_type)
    
    def create_provider(self, provider_type: APIProvider, **kwargs) -> LLMProvider:
        """Create a new provider instance"""
        if provider_type == APIProvider.OPENAI:
            return OpenAIProvider(**kwargs)
        elif provider_type == APIProvider.ANTHROPIC:
            return AnthropicProvider(**kwargs)
        elif provider_type == APIProvider.OLLAMA:
            return OllamaProvider(**kwargs)
        elif provider_type == APIProvider.CUSTOM:
            return CustomProvider(**kwargs)
        else:
            raise ValueError(f"Unsupported provider type: {provider_type}")
