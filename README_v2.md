# BlenderGPT v2.1.0 - Multi-Provider AI Assistant
![Header](https://user-images.githubusercontent.com/63528145/227160213-6862cd5e-b31f-43ea-a5e5-6cc340a95617.png)

## 🚀 **What's New in v2.1.0**

### ✨ **Multi-Provider Support**
- **OpenAI**: GPT-4, GPT-3.5 Turbo
- **Anthropic**: Claude 3 (Opus, Sonnet, Haiku)
- **Ollama**: Local LLMs (Llama 2, Code Llama, Mistral, etc.)
- **Custom APIs**: Any OpenAI-compatible API

### 🔒 **Enhanced Security**
- **Code Preview**: Review AI-generated code before execution
- **Security Validation**: Automatic detection of potentially dangerous operations
- **Safe Execution Environment**: Restricted code execution with limited permissions

### 🎯 **Improved User Experience**
- **Context Awareness**: AI understands your current scene and selection
- **Quick Actions**: Pre-defined commands for common tasks
- **Better Error Handling**: More informative error messages and recovery
- **Enhanced UI**: Modern interface with better organization

---

## 📖 **Overview**

<PERSON><PERSON><PERSON><PERSON><PERSON> is an advanced AI assistant for Blender that supports multiple Large Language Model providers. Generate Python scripts from natural language commands and execute them safely within Blender's environment.

### **Key Features:**
- 🤖 **Multi-AI Support**: Choose from OpenAI, Anthropic, Ollama, or custom providers
- 🔐 **Security First**: Built-in code validation and preview system
- 🎨 **Context Aware**: Understands your current scene and objects
- ⚡ **Quick Actions**: One-click common operations
- 📝 **Chat History**: Keep track of your AI conversations
- 🛡️ **Safe Execution**: Restricted environment prevents harmful operations

---

## 🛠️ **Installation**

### **Method 1: Download ZIP (Recommended)**
1. Click `Code > Download ZIP` on GitHub
2. Open Blender → `Edit > Preferences > Add-ons > Install`
3. Select the downloaded ZIP file and click `Install Add-on`
4. Enable the add-on by checking `BlenderGPT - Multi-Provider AI Assistant`
5. Configure your API keys in the addon preferences

### **Method 2: Git Clone**
```bash
git clone https://github.com/gd3kr/BlenderGPT.git
cd BlenderGPT
# Copy to Blender addons directory
```

---

## ⚙️ **Configuration**

### **API Keys Setup**
1. Go to `Edit > Preferences > Add-ons > BlenderGPT > Preferences`
2. Enter your API keys:
   - **Primary API Key**: For OpenAI and Custom providers
   - **Anthropic API Key**: For Claude models

### **Get API Keys:**
- **OpenAI**: [platform.openai.com/api-keys](https://platform.openai.com/api-keys)
- **Anthropic**: [console.anthropic.com](https://console.anthropic.com/)
- **Ollama**: No API key needed (local installation)

### **Provider-Specific Setup**

#### **OpenAI**
- Requires API key
- Supports: GPT-4, GPT-3.5 Turbo
- Cost: Pay per token

#### **Anthropic Claude**
- Requires separate API key
- Supports: Claude 3 Opus, Sonnet, Haiku
- Cost: Pay per token

#### **Ollama (Local)**
1. Install Ollama: [ollama.ai](https://ollama.ai)
2. Pull a model: `ollama pull llama2`
3. Start Ollama service
4. Set Base URL to `http://localhost:11434` (default)

#### **Custom API**
- Any OpenAI-compatible API
- Set custom Base URL
- Use your API key

---

## 🎮 **Usage**

### **Basic Usage**
1. Open Blender and press `N` to show the sidebar
2. Navigate to the `BlenderGPT` tab
3. Select your preferred AI provider
4. Choose a model
5. Type your command (e.g., "Create a red cube at the origin")
6. Click `🚀 Execute`

### **Example Commands**
```
"Create 10 cubes in random locations"
"Apply a red material to all selected objects"
"Arrange selected objects in a circle"
"Create a simple character rig"
"Add a camera looking at the origin"
"Generate a procedural tree"
```

### **Quick Actions**
Use pre-defined buttons for common tasks:
- **Add Cube**: Creates a cube at origin
- **Delete Selected**: Removes selected objects
- **Random Colors**: Applies random materials
- **Arrange Grid**: Organizes objects in a grid

### **Security Features**
- **Enable Security Validation**: Scans code for dangerous operations
- **Show Code Preview**: Review generated code before execution
- Both options are enabled by default for safety

---

## 🔧 **Advanced Features**

### **Context Awareness**
The AI understands your current scene:
- Selected objects
- Active object
- Current mode (Edit, Object, etc.)
- Scene statistics

### **Chat History**
- Maintains conversation context
- Shows last 5 messages in UI
- Delete individual messages
- Clear entire history

### **Error Handling**
- Detailed error messages
- Safe execution environment
- Automatic recovery suggestions
- Debug information in console

---

## 📋 **Requirements**

- **Blender**: 3.1 or later
- **Python**: 3.9+ (included with Blender)
- **Internet**: For cloud-based providers
- **API Keys**: For OpenAI/Anthropic (see configuration)

### **Dependencies**
```
requests>=2.28.0
aiohttp>=3.8.0
anthropic>=0.3.0
```

---

## 🚨 **Security & Safety**

### **Built-in Protections**
- Code validation before execution
- Restricted execution environment
- Dangerous function detection
- File system access prevention

### **Best Practices**
1. **Always enable security validation**
2. **Review code before execution**
3. **Use trusted AI providers**
4. **Keep API keys secure**
5. **Regular backups of your work**

### **Blocked Operations**
- File system access (`os`, `sys`, `subprocess`)
- Network operations (`requests`, `urllib`)
- Code execution (`eval`, `exec`, `compile`)
- System commands
- Dangerous imports

---

## 🐛 **Troubleshooting**

### **Common Issues**

#### **"No API key detected"**
- Check addon preferences
- Verify API key is correct
- Ensure provider is properly selected

#### **"Connection failed"**
- Check internet connection
- Verify API endpoint URL
- Check API key permissions

#### **"Code execution blocked"**
- Review security warnings
- Disable security validation if needed
- Check for dangerous operations

#### **Ollama not working**
- Ensure Ollama is installed and running
- Check if model is downloaded
- Verify Base URL (default: `http://localhost:11434`)

### **Debug Mode**
Enable Blender's console to see detailed logs:
- Windows: `Window > Toggle System Console`
- macOS/Linux: Run Blender from terminal

---

## 🤝 **Contributing**

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### **Development Setup**
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE.md](LICENSE.md) file for details.

---

## 🙏 **Acknowledgments**

- Original BlenderGPT by [@gd3kr](https://github.com/gd3kr)
- OpenAI for GPT models
- Anthropic for Claude models
- Ollama team for local LLM support
- Blender Foundation
- All contributors and users

---

## 📞 **Support**

- **Issues**: [GitHub Issues](https://github.com/gd3kr/BlenderGPT/issues)
- **Discussions**: [GitHub Discussions](https://github.com/gd3kr/BlenderGPT/discussions)
- **Wiki**: [Project Wiki](https://github.com/gd3kr/BlenderGPT/wiki)

---

**⚠️ Disclaimer**: This addon executes AI-generated code. While we implement security measures, always review generated code and use at your own risk. Keep backups of important work.
