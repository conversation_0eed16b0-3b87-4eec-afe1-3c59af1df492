import bpy
import re
import os
import sys
import json
import threading
import time
from typing import Dict, List, Optional, Tuple, Any
from .api_providers import APIProviderManager, APIProvider
from .security import validate_and_preview_code, create_safe_execution_environment


def get_api_key(context, addon_name):
    preferences = context.preferences
    addon_prefs = preferences.addons[addon_name].preferences
    return addon_prefs.api_key


def init_props():
    """Initialize Blender properties for the addon"""
    bpy.types.Scene.gpt4_chat_history = bpy.props.CollectionProperty(type=bpy.types.PropertyGroup)

    # API Provider selection
    bpy.types.Scene.gpt4_api_provider = bpy.props.EnumProperty(
        name="API Provider",
        description="Select the API provider to use",
        items=[
            ("openai", "OpenAI", "Use OpenAI API"),
            ("anthropic", "Anthropic Claude", "Use Anthropic Claude API"),
            ("ollama", "Ollama (Local)", "Use local Ollama API"),
            ("custom", "Custom API", "Use custom OpenAI-compatible API"),
        ],
        default="openai",
    )

    # Model selection (dynamic based on provider)
    bpy.types.Scene.gpt4_model = bpy.props.StringProperty(
        name="Model",
        description="Model to use",
        default="gpt-4",
    )

    # Custom API settings
    bpy.types.Scene.gpt4_custom_base_url = bpy.props.StringProperty(
        name="Custom Base URL",
        description="Base URL for custom API",
        default="http://localhost:8000/v1",
    )

    bpy.types.Scene.gpt4_chat_input = bpy.props.StringProperty(
        name="Message",
        description="Enter your message",
        default="",
    )

    bpy.types.Scene.gpt4_button_pressed = bpy.props.BoolProperty(default=False)
    bpy.types.Scene.gpt4_code_preview_enabled = bpy.props.BoolProperty(
        name="Code Preview",
        description="Show code preview before execution",
        default=True,
    )
    bpy.types.Scene.gpt4_security_enabled = bpy.props.BoolProperty(
        name="Security Validation",
        description="Enable security validation for generated code",
        default=True,
    )

    # Message properties
    bpy.types.PropertyGroup.type = bpy.props.StringProperty()
    bpy.types.PropertyGroup.content = bpy.props.StringProperty()
    bpy.types.PropertyGroup.timestamp = bpy.props.FloatProperty()
    bpy.types.PropertyGroup.approved = bpy.props.BoolProperty(default=False)

def clear_props():
    """Clear Blender properties when addon is disabled"""
    try:
        del bpy.types.Scene.gpt4_chat_history
        del bpy.types.Scene.gpt4_api_provider
        del bpy.types.Scene.gpt4_model
        del bpy.types.Scene.gpt4_custom_base_url
        del bpy.types.Scene.gpt4_chat_input
        del bpy.types.Scene.gpt4_button_pressed
        del bpy.types.Scene.gpt4_code_preview_enabled
        del bpy.types.Scene.gpt4_security_enabled
        del bpy.types.PropertyGroup.type
        del bpy.types.PropertyGroup.content
        del bpy.types.PropertyGroup.timestamp
        del bpy.types.PropertyGroup.approved
    except AttributeError:
        # Properties may not exist if addon failed to initialize
        pass

def generate_blender_code(prompt: str, chat_history, context, system_prompt: str) -> Optional[str]:
    """
    Generate Blender code using the selected API provider
    """
    try:
        # Get API provider manager
        provider_manager = APIProviderManager()

        # Get provider settings from context
        provider_type = APIProvider(context.scene.gpt4_api_provider)
        api_key = get_api_key(context, __name__)

        # Create provider instance
        if provider_type == APIProvider.OPENAI:
            provider = provider_manager.create_provider(
                provider_type,
                api_key=api_key,
                model=context.scene.gpt4_model
            )
        elif provider_type == APIProvider.ANTHROPIC:
            provider = provider_manager.create_provider(
                provider_type,
                api_key=api_key,
                model=context.scene.gpt4_model
            )
        elif provider_type == APIProvider.OLLAMA:
            provider = provider_manager.create_provider(
                provider_type,
                base_url=context.scene.gpt4_custom_base_url or "http://localhost:11434",
                model=context.scene.gpt4_model
            )
        elif provider_type == APIProvider.CUSTOM:
            provider = provider_manager.create_provider(
                provider_type,
                api_key=api_key,
                base_url=context.scene.gpt4_custom_base_url,
                model=context.scene.gpt4_model
            )
        else:
            raise ValueError(f"Unsupported provider: {provider_type}")

        # Prepare messages
        messages = [{"role": "system", "content": system_prompt}]

        # Add chat history (last 10 messages)
        for message in chat_history[-10:]:
            if message.type == "assistant":
                messages.append({"role": "assistant", "content": message.content})
            else:
                messages.append({"role": message.type.lower(), "content": message.content})

        # Add the current user message
        enhanced_prompt = f"Can you please write Blender code for me that accomplishes the following task: {prompt}? \n. Do not respond with anything that is not Python code. Do not provide explanations"
        messages.append({"role": "user", "content": enhanced_prompt})

        # Generate code using streaming
        completion_text = ''

        try:
            for event in provider.create_chat_completion_stream(messages, max_tokens=1500):
                if 'choices' in event and len(event['choices']) > 0:
                    delta = event['choices'][0].get('delta', {})
                    if 'content' in delta:
                        content = delta['content']
                        completion_text += content
                        print(completion_text, flush=True, end='\r')
        except Exception as stream_error:
            print(f"Streaming failed, falling back to non-streaming: {stream_error}")
            # Fallback to non-streaming
            response = provider.create_chat_completion(messages, max_tokens=1500)
            if 'choices' in response and len(response['choices']) > 0:
                completion_text = response['choices'][0]['message']['content']

        # Extract code from markdown blocks
        code_blocks = re.findall(r'```(?:python)?\s*(.*?)```', completion_text, re.DOTALL)
        if code_blocks:
            completion_text = code_blocks[0].strip()
        else:
            # If no code blocks found, use the entire response
            completion_text = completion_text.strip()

        # Remove any remaining language identifiers
        completion_text = re.sub(r'^python\s*\n', '', completion_text, flags=re.MULTILINE)

        return completion_text if completion_text else None

    except Exception as e:
        print(f"Error generating code: {e}")
        return None

def execute_code_safely(code: str, context) -> Tuple[bool, str]:
    """
    Execute code safely with security validation and preview
    Returns: (success, error_message)
    """
    try:
        # Security validation if enabled
        if context.scene.gpt4_security_enabled:
            approved, validated_code = validate_and_preview_code(code)
            if not approved:
                return False, "Code execution blocked by security validation"
            code = validated_code

        # Create safe execution environment
        safe_globals = create_safe_execution_environment()

        # Execute the code
        exec(code, safe_globals)
        return True, ""

    except Exception as e:
        error_msg = f"Error executing generated code: {str(e)}"
        print(error_msg)
        return False, error_msg

def get_scene_context(context) -> Dict[str, Any]:
    """Get current scene context for better AI understanding"""
    scene_info = {
        "selected_objects": [obj.name for obj in context.selected_objects],
        "active_object": context.active_object.name if context.active_object else None,
        "scene_objects": [obj.name for obj in context.scene.objects],
        "current_mode": context.mode,
        "cursor_location": list(context.scene.cursor.location),
        "frame_current": context.scene.frame_current,
    }
    return scene_info

def enhance_prompt_with_context(prompt: str, context) -> str:
    """Enhance user prompt with scene context"""
    scene_info = get_scene_context(context)

    context_str = f"""
Current scene context:
- Selected objects: {', '.join(scene_info['selected_objects']) if scene_info['selected_objects'] else 'None'}
- Active object: {scene_info['active_object'] or 'None'}
- Current mode: {scene_info['current_mode']}
- Total objects in scene: {len(scene_info['scene_objects'])}

User request: {prompt}
"""
    return context_str

def split_area_to_text_editor(context):
    """Split current area to show text editor"""
    area = context.area
    for region in area.regions:
        if region.type == 'WINDOW':
            override = {'area': area, 'region': region}
            bpy.ops.screen.area_split(override, direction='VERTICAL', factor=0.5)
            break

    new_area = context.screen.areas[-1]
    new_area.type = 'TEXT_EDITOR'
    return new_area

def get_available_models(provider_type: str, api_key: str = "", base_url: str = "") -> List[str]:
    """Get available models for the selected provider"""
    try:
        provider_manager = APIProviderManager()
        provider_enum = APIProvider(provider_type)

        if provider_enum == APIProvider.OPENAI:
            provider = provider_manager.create_provider(provider_enum, api_key=api_key, model="gpt-4")
        elif provider_enum == APIProvider.ANTHROPIC:
            provider = provider_manager.create_provider(provider_enum, api_key=api_key, model="claude-3-sonnet-20240229")
        elif provider_enum == APIProvider.OLLAMA:
            provider = provider_manager.create_provider(provider_enum, base_url=base_url or "http://localhost:11434", model="llama2")
        elif provider_enum == APIProvider.CUSTOM:
            provider = provider_manager.create_provider(provider_enum, api_key=api_key, base_url=base_url, model="gpt-3.5-turbo")
        else:
            return []

        return provider.get_available_models()
    except Exception as e:
        print(f"Error getting models: {e}")
        return []