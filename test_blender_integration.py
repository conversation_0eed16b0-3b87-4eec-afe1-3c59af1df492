"""
Simple test script for BlenderGPT integration
This script tests basic functionality without requiring full Blender environment
"""

def test_basic_imports():
    """Test if our modules can be imported"""
    print("Testing basic imports...")
    
    try:
        # Test security module
        from security import CodeSecurityValidator
        validator = CodeSecurityValidator()
        print("✅ Security module imported successfully")
        
        # Test a simple validation
        test_code = "import bpy\nbpy.ops.mesh.primitive_cube_add()"
        is_safe, warnings, errors = validator.validate_code(test_code)
        print(f"✅ Security validation works: safe={is_safe}, warnings={len(warnings)}, errors={len(errors)}")
        
    except Exception as e:
        print(f"❌ Security module error: {e}")
    
    try:
        # Test API providers module
        from api_providers import APIProviderManager, APIProvider
        manager = APIProviderManager()
        print("✅ API providers module imported successfully")
        
    except Exception as e:
        print(f"❌ API providers module error: {e}")

def test_security_validation():
    """Test security validation with various code samples"""
    print("\nTesting security validation...")
    
    try:
        from security import CodeSecurityValidator
        validator = CodeSecurityValidator()
        
        test_cases = [
            ("Safe Blender code", "import bpy\nbpy.ops.mesh.primitive_cube_add()", True),
            ("Dangerous import", "import os\nos.system('rm -rf /')", False),
            ("Dangerous function", "exec('malicious code')", False),
        ]
        
        for name, code, should_be_safe in test_cases:
            is_safe, warnings, errors = validator.validate_code(code)
            status = "✅" if (is_safe == should_be_safe) else "❌"
            print(f"{status} {name}: safe={is_safe} (expected={should_be_safe})")
            
            if errors:
                print(f"   Errors: {errors}")
            if warnings:
                print(f"   Warnings: {warnings}")
                
    except Exception as e:
        print(f"❌ Security validation test failed: {e}")

def test_api_providers():
    """Test API provider creation"""
    print("\nTesting API providers...")
    
    try:
        from api_providers import APIProviderManager, APIProvider
        manager = APIProviderManager()
        
        # Test provider creation (without actual API calls)
        providers_to_test = [
            (APIProvider.OPENAI, {"api_key": "test", "model": "gpt-4"}),
            (APIProvider.OLLAMA, {"base_url": "http://localhost:11434", "model": "llama2"}),
        ]
        
        for provider_type, kwargs in providers_to_test:
            try:
                provider = manager.create_provider(provider_type, **kwargs)
                print(f"✅ {provider_type.value} provider created successfully")
            except Exception as e:
                print(f"❌ {provider_type.value} provider creation failed: {e}")
                
    except Exception as e:
        print(f"❌ API providers test failed: {e}")

def test_utilities():
    """Test utilities functions"""
    print("\nTesting utilities...")
    
    try:
        # Test without Blender context (will use fallbacks)
        import utilities
        print("✅ Utilities module imported successfully")
        
        # Test some utility functions that don't require Blender
        if hasattr(utilities, 'get_available_models'):
            print("✅ get_available_models function exists")
        
    except Exception as e:
        print(f"❌ Utilities test failed: {e}")

def main():
    """Run all tests"""
    print("🚀 BlenderGPT Integration Test Suite")
    print("=" * 50)
    
    test_basic_imports()
    test_security_validation()
    test_api_providers()
    test_utilities()
    
    print("\n" + "=" * 50)
    print("✅ Test suite completed!")
    print("\n💡 Next steps:")
    print("1. Install the addon in Blender")
    print("2. Configure your API keys in preferences")
    print("3. Test with simple commands like 'create a cube'")
    print("4. Enable security validation for safe usage")

if __name__ == "__main__":
    main()
