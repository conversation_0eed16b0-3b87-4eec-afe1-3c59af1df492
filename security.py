"""
BlenderGPT Security Module
Provides code validation and safety checks for AI-generated code
"""

import ast
import re
import bpy
from typing import List, Tuple, Dict, Any

class CodeSecurityValidator:
    """Validates AI-generated code for security risks"""
    
    # Dangerous functions/modules that should be blocked
    DANGEROUS_IMPORTS = {
        'os', 'sys', 'subprocess', 'shutil', 'pathlib', 'glob',
        'socket', 'urllib', 'requests', 'http', 'ftplib',
        'smtplib', 'poplib', 'imaplib', 'telnetlib',
        'pickle', 'marshal', 'shelve', 'dbm',
        '__import__', 'eval', 'exec', 'compile'
    }
    
    DANGEROUS_FUNCTIONS = {
        'exec', 'eval', 'compile', '__import__',
        'open', 'file', 'input', 'raw_input',
        'exit', 'quit', 'reload'
    }
    
    DANGEROUS_ATTRIBUTES = {
        '__class__', '__bases__', '__subclasses__',
        '__globals__', '__locals__', '__dict__',
        '__code__', '__func__'
    }
    
    # Allowed Blender modules
    ALLOWED_IMPORTS = {
        'bpy', 'bmesh', 'mathutils', 'bgl', 'blf',
        'gpu', 'gpu_extras', 'bpy_extras',
        'math', 'random', 'time', 'datetime',
        'json', 're', 'collections', 'itertools'
    }

    def __init__(self):
        self.warnings = []
        self.errors = []
        
    def validate_code(self, code: str) -> Tuple[bool, List[str], List[str]]:
        """
        Validate code for security risks
        Returns: (is_safe, warnings, errors)
        """
        self.warnings.clear()
        self.errors.clear()
        
        try:
            # Parse the code into AST
            tree = ast.parse(code)
            
            # Check for dangerous patterns
            self._check_imports(tree)
            self._check_function_calls(tree)
            self._check_attribute_access(tree)
            self._check_file_operations(tree)
            
            # Additional pattern-based checks
            self._check_string_patterns(code)
            
            is_safe = len(self.errors) == 0
            return is_safe, self.warnings.copy(), self.errors.copy()
            
        except SyntaxError as e:
            self.errors.append(f"Syntax error in generated code: {e}")
            return False, self.warnings.copy(), self.errors.copy()
        except Exception as e:
            self.errors.append(f"Code validation error: {e}")
            return False, self.warnings.copy(), self.errors.copy()
    
    def _check_imports(self, tree: ast.AST):
        """Check for dangerous imports"""
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    module_name = alias.name.split('.')[0]
                    if module_name in self.DANGEROUS_IMPORTS:
                        self.errors.append(f"Dangerous import detected: {alias.name}")
                    elif module_name not in self.ALLOWED_IMPORTS:
                        self.warnings.append(f"Unusual import: {alias.name}")
                        
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    module_name = node.module.split('.')[0]
                    if module_name in self.DANGEROUS_IMPORTS:
                        self.errors.append(f"Dangerous import detected: {node.module}")
                    elif module_name not in self.ALLOWED_IMPORTS:
                        self.warnings.append(f"Unusual import: {node.module}")
    
    def _check_function_calls(self, tree: ast.AST):
        """Check for dangerous function calls"""
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                func_name = self._get_function_name(node.func)
                if func_name in self.DANGEROUS_FUNCTIONS:
                    self.errors.append(f"Dangerous function call: {func_name}")
    
    def _check_attribute_access(self, tree: ast.AST):
        """Check for dangerous attribute access"""
        for node in ast.walk(tree):
            if isinstance(node, ast.Attribute):
                if node.attr in self.DANGEROUS_ATTRIBUTES:
                    self.errors.append(f"Dangerous attribute access: {node.attr}")
    
    def _check_file_operations(self, tree: ast.AST):
        """Check for file system operations"""
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                func_name = self._get_function_name(node.func)
                if func_name in ['open', 'file']:
                    self.warnings.append("File operation detected - please review carefully")
    
    def _check_string_patterns(self, code: str):
        """Check for dangerous string patterns"""
        dangerous_patterns = [
            r'rm\s+-rf',
            r'del\s+/[sq]',
            r'format\s*\(',
            r'\.format\s*\(',
            r'%\s*\(',
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, code, re.IGNORECASE):
                self.warnings.append(f"Potentially dangerous pattern detected: {pattern}")
    
    def _get_function_name(self, func_node) -> str:
        """Extract function name from AST node"""
        if isinstance(func_node, ast.Name):
            return func_node.id
        elif isinstance(func_node, ast.Attribute):
            return func_node.attr
        return ""

class CodePreviewDialog:
    """Dialog for previewing and approving AI-generated code"""
    
    @staticmethod
    def show_preview(code: str, warnings: List[str], errors: List[str]) -> bool:
        """
        Show code preview dialog
        Returns True if user approves execution
        """
        # This will be implemented as a Blender modal dialog
        # For now, we'll use console output
        print("\n" + "="*60)
        print("AI GENERATED CODE PREVIEW")
        print("="*60)
        print(code)
        print("="*60)
        
        if errors:
            print("SECURITY ERRORS:")
            for error in errors:
                print(f"  ❌ {error}")
            print("CODE EXECUTION BLOCKED!")
            return False
            
        if warnings:
            print("WARNINGS:")
            for warning in warnings:
                print(f"  ⚠️  {warning}")
        
        print("="*60)
        # In real implementation, this would be a modal dialog
        return True  # For now, always approve if no errors

def validate_and_preview_code(code: str) -> Tuple[bool, str]:
    """
    Main function to validate and preview code
    Returns: (approved, sanitized_code)
    """
    validator = CodeSecurityValidator()
    is_safe, warnings, errors = validator.validate_code(code)
    
    if not is_safe:
        # Show preview dialog
        approved = CodePreviewDialog.show_preview(code, warnings, errors)
        if not approved:
            return False, ""
    
    # If we get here, code is approved
    return True, code

def create_safe_execution_environment():
    """Create a restricted execution environment"""
    safe_globals = {
        '__builtins__': {
            'len': len, 'range': range, 'enumerate': enumerate,
            'zip': zip, 'map': map, 'filter': filter,
            'str': str, 'int': int, 'float': float, 'bool': bool,
            'list': list, 'dict': dict, 'tuple': tuple, 'set': set,
            'min': min, 'max': max, 'sum': sum, 'abs': abs,
            'round': round, 'sorted': sorted, 'reversed': reversed,
            'print': print  # Allow print for debugging
        },
        'bpy': bpy,
        'math': __import__('math'),
        'random': __import__('random')
    }
    return safe_globals
