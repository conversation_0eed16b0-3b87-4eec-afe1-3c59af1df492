import sys
import os
import time
import bpy
import bpy.props
import re

# Add the 'libs' folder to the Python path
libs_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "lib")
if libs_path not in sys.path:
    sys.path.append(libs_path)

from .utilities import *
bl_info = {
    "name": "BlenderGPT - Multi-Provider AI Assistant",
    "blender": (3, 1, 0),
    "category": "Object",
    "author": "<PERSON><PERSON><PERSON> (@gd3kr), Enhanced by Community",
    "version": (2, 1, 0),
    "location": "3D View > UI > BlenderGPT Assistant",
    "description": "Generate Blender Python code using multiple AI providers (OpenAI, Anthropic, Ollama, Custom APIs) with enhanced security and features.",
    "warning": "This addon executes AI-generated code. Enable security validation for safer usage.",
    "wiki_url": "https://github.com/gd3kr/BlenderGPT",
    "tracker_url": "https://github.com/gd3kr/BlenderGPT/issues",
    "doc_url": "https://github.com/gd3kr/BlenderGPT/wiki",
}

system_prompt = """You are an assistant made for the purposes of helping the user with Blender, the 3D software. 
- Respond with your answers in markdown (```). 
- Preferably import entire modules instead of bits. 
- Do not perform destructive operations on the meshes. 
- Do not use cap_ends. Do not do more than what is asked (setting up render settings, adding cameras, etc)
- Do not respond with anything that is not Python code.

Example:

user: create 10 cubes in random locations from -10 to 10
assistant:
```
import bpy
import random
bpy.ops.mesh.primitive_cube_add()

#how many cubes you want to add
count = 10

for c in range(0,count):
    x = random.randint(-10,10)
    y = random.randint(-10,10)
    z = random.randint(-10,10)
    bpy.ops.mesh.primitive_cube_add(location=(x,y,z))
```"""



class GPT4_OT_DeleteMessage(bpy.types.Operator):
    bl_idname = "gpt4.delete_message"
    bl_label = "Delete Message"
    bl_options = {'REGISTER', 'UNDO'}

    message_index: bpy.props.IntProperty()

    def execute(self, context):
        context.scene.gpt4_chat_history.remove(self.message_index)
        return {'FINISHED'}

class GPT4_OT_ShowCode(bpy.types.Operator):
    bl_idname = "gpt4.show_code"
    bl_label = "Show Code"
    bl_options = {'REGISTER', 'UNDO'}

    code: bpy.props.StringProperty(
        name="Code",
        description="The generated code",
        default="",
    )

    def execute(self, context):
        text_name = "GPT4_Generated_Code.py"
        text = bpy.data.texts.get(text_name)
        if text is None:
            text = bpy.data.texts.new(text_name)

        text.clear()
        text.write(self.code)

        text_editor_area = None
        for area in context.screen.areas:
            if area.type == 'TEXT_EDITOR':
                text_editor_area = area
                break

        if text_editor_area is None:
            text_editor_area = split_area_to_text_editor(context)
        
        text_editor_area.spaces.active.text = text

        return {'FINISHED'}

class GPT4_PT_Panel(bpy.types.Panel):
    bl_label = "BlenderGPT Assistant"
    bl_idname = "GPT4_PT_Panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'BlenderGPT'

    def draw(self, context):
        layout = self.layout
        column = layout.column(align=True)

        # API Provider Settings
        box = column.box()
        box.label(text="API Settings:", icon="SETTINGS")
        box.prop(context.scene, "gpt4_api_provider", text="Provider")

        # Model selection
        row = box.row()
        row.prop(context.scene, "gpt4_model", text="Model")
        refresh_op = row.operator("gpt4.refresh_models", text="", icon="FILE_REFRESH")

        # Custom API URL for Ollama/Custom providers
        if context.scene.gpt4_api_provider in ['ollama', 'custom']:
            box.prop(context.scene, "gpt4_custom_base_url", text="Base URL")

        # Security settings
        security_box = column.box()
        security_box.label(text="Security:", icon="LOCKED")
        security_box.prop(context.scene, "gpt4_security_enabled", text="Enable Security Validation")
        security_box.prop(context.scene, "gpt4_code_preview_enabled", text="Show Code Preview")

        column.separator()

        # Chat history
        column.label(text="Chat History:", icon="CHAT")
        box = column.box()

        if len(context.scene.gpt4_chat_history) == 0:
            box.label(text="No messages yet", icon="INFO")
        else:
            for index, message in enumerate(context.scene.gpt4_chat_history[-5:]):  # Show last 5 messages
                if message.type == 'assistant':
                    row = box.row()
                    row.label(text="🤖 Assistant:", icon="NONE")
                    show_code_op = row.operator("gpt4.show_code", text="", icon="SCRIPT")
                    show_code_op.code = message.content
                    delete_message_op = row.operator("gpt4.delete_message", text="", icon="TRASH", emboss=False)
                    delete_message_op.message_index = len(context.scene.gpt4_chat_history) - 5 + index
                else:
                    row = box.row()
                    # Truncate long messages
                    display_text = message.content[:50] + "..." if len(message.content) > 50 else message.content
                    row.label(text=f"👤 {display_text}")
                    delete_message_op = row.operator("gpt4.delete_message", text="", icon="TRASH", emboss=False)
                    delete_message_op.message_index = len(context.scene.gpt4_chat_history) - 5 + index

        column.separator()

        # Input section
        input_box = column.box()
        input_box.label(text="Enter Command:", icon="CONSOLE")
        input_box.prop(context.scene, "gpt4_chat_input", text="", placeholder="e.g., 'Create a cube at the origin'")

        # Buttons
        button_label = "⏳ Processing..." if context.scene.gpt4_button_pressed else "🚀 Execute"
        row = input_box.row(align=True)
        execute_op = row.operator("gpt4.send_message", text=button_label)
        execute_op.enabled = not context.scene.gpt4_button_pressed
        row.operator("gpt4.clear_chat", text="🗑️ Clear")

        column.separator()

        # Quick actions
        quick_box = column.box()
        quick_box.label(text="Quick Actions:", icon="LIGHTBULB")
        quick_row1 = quick_box.row(align=True)
        quick_row1.operator("gpt4.quick_action", text="Add Cube").action = "add_cube"
        quick_row1.operator("gpt4.quick_action", text="Delete Selected").action = "delete_selected"
        quick_row2 = quick_box.row(align=True)
        quick_row2.operator("gpt4.quick_action", text="Random Colors").action = "random_colors"
        quick_row2.operator("gpt4.quick_action", text="Arrange Grid").action = "arrange_grid"

class GPT4_OT_ClearChat(bpy.types.Operator):
    bl_idname = "gpt4.clear_chat"
    bl_label = "Clear Chat"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        context.scene.gpt4_chat_history.clear()
        self.report({'INFO'}, "Chat history cleared")
        return {'FINISHED'}

class GPT4_OT_RefreshModels(bpy.types.Operator):
    bl_idname = "gpt4.refresh_models"
    bl_label = "Refresh Models"
    bl_description = "Refresh available models for the selected provider"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            api_key = get_api_key(context, __name__)
            provider_type = context.scene.gpt4_api_provider
            base_url = context.scene.gpt4_custom_base_url

            models = get_available_models(provider_type, api_key, base_url)

            if models:
                # Update the model if current one is not in the list
                if context.scene.gpt4_model not in models:
                    context.scene.gpt4_model = models[0]
                self.report({'INFO'}, f"Found {len(models)} models")
            else:
                self.report({'WARNING'}, "No models found or connection failed")

        except Exception as e:
            self.report({'ERROR'}, f"Failed to refresh models: {str(e)}")

        return {'FINISHED'}

class GPT4_OT_QuickAction(bpy.types.Operator):
    bl_idname = "gpt4.quick_action"
    bl_label = "Quick Action"
    bl_description = "Execute a predefined quick action"
    bl_options = {'REGISTER', 'UNDO'}

    action: bpy.props.StringProperty()

    def execute(self, context):
        quick_actions = {
            "add_cube": "Add a cube at the origin",
            "delete_selected": "Delete all selected objects",
            "random_colors": "Apply random colors to all selected objects",
            "arrange_grid": "Arrange all selected objects in a 3x3 grid"
        }

        if self.action in quick_actions:
            # Set the command and trigger execution
            context.scene.gpt4_chat_input = quick_actions[self.action]
            bpy.ops.gpt4.send_message()
        else:
            self.report({'ERROR'}, f"Unknown quick action: {self.action}")

        return {'FINISHED'}

class GPT4_OT_Execute(bpy.types.Operator):
    bl_idname = "gpt4.send_message"
    bl_label = "Send Message"
    bl_options = {'REGISTER', 'UNDO'}

    natural_language_input: bpy.props.StringProperty(
        name="Command",
        description="Enter the natural language command",
        default="",
    )

    def execute(self, context):
        # Validate API key for providers that need it
        api_key = get_api_key(context, __name__)
        if context.scene.gpt4_api_provider in ['openai', 'anthropic', 'custom'] and not api_key:
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                self.report({'ERROR'}, "No API key detected. Please set the API key in the addon preferences.")
                return {'CANCELLED'}

        # Validate input
        if not context.scene.gpt4_chat_input.strip():
            self.report({'ERROR'}, "Please enter a command.")
            return {'CANCELLED'}

        context.scene.gpt4_button_pressed = True
        bpy.ops.wm.redraw_timer(type='DRAW_WIN_SWAP', iterations=1)

        try:
            # Enhance prompt with scene context
            enhanced_prompt = enhance_prompt_with_context(context.scene.gpt4_chat_input, context)

            # Generate code
            blender_code = generate_blender_code(
                enhanced_prompt,
                context.scene.gpt4_chat_history,
                context,
                system_prompt
            )

            # Add user message to history
            user_message = context.scene.gpt4_chat_history.add()
            user_message.type = 'user'
            user_message.content = context.scene.gpt4_chat_input
            user_message.timestamp = time.time()

            # Clear the chat input field
            user_input = context.scene.gpt4_chat_input
            context.scene.gpt4_chat_input = ""

            if blender_code:
                # Add assistant message to history
                assistant_message = context.scene.gpt4_chat_history.add()
                assistant_message.type = 'assistant'
                assistant_message.content = blender_code
                assistant_message.timestamp = time.time()

                # Execute code safely
                success, error_msg = execute_code_safely(blender_code, context)

                if success:
                    assistant_message.approved = True
                    self.report({'INFO'}, f"Successfully executed: {user_input}")
                else:
                    assistant_message.approved = False
                    self.report({'ERROR'}, error_msg)
                    context.scene.gpt4_button_pressed = False
                    return {'CANCELLED'}
            else:
                self.report({'ERROR'}, "Failed to generate code. Please try again.")
                context.scene.gpt4_button_pressed = False
                return {'CANCELLED'}

        except Exception as e:
            self.report({'ERROR'}, f"Unexpected error: {str(e)}")
            context.scene.gpt4_button_pressed = False
            return {'CANCELLED'}

        context.scene.gpt4_button_pressed = False
        return {'FINISHED'}


def menu_func(self, context):
    self.layout.operator(GPT4_OT_Execute.bl_idname)

class GPT4AddonPreferences(bpy.types.AddonPreferences):
    bl_idname = __name__

    api_key: bpy.props.StringProperty(
        name="API Key",
        description="Enter your API Key (OpenAI, Anthropic, or Custom)",
        default="",
        subtype="PASSWORD",
    )

    anthropic_api_key: bpy.props.StringProperty(
        name="Anthropic API Key",
        description="Enter your Anthropic API Key",
        default="",
        subtype="PASSWORD",
    )

    def draw(self, context):
        layout = self.layout

        # Main API Key
        box = layout.box()
        box.label(text="API Keys:", icon="KEY_HLT")
        box.prop(self, "api_key", text="Primary API Key")
        box.prop(self, "anthropic_api_key", text="Anthropic API Key")

        # Information
        info_box = layout.box()
        info_box.label(text="Information:", icon="INFO")
        info_box.label(text="• Primary API Key: Used for OpenAI and Custom providers")
        info_box.label(text="• Anthropic API Key: Used specifically for Claude models")
        info_box.label(text="• Ollama: No API key required (local)")

        # Links
        links_box = layout.box()
        links_box.label(text="Get API Keys:", icon="URL")
        links_box.operator("wm.url_open", text="OpenAI API Keys", icon="URL").url = "https://platform.openai.com/api-keys"
        links_box.operator("wm.url_open", text="Anthropic API Keys", icon="URL").url = "https://console.anthropic.com/"

def register():
    # Register all classes
    bpy.utils.register_class(GPT4AddonPreferences)
    bpy.utils.register_class(GPT4_OT_Execute)
    bpy.utils.register_class(GPT4_PT_Panel)
    bpy.utils.register_class(GPT4_OT_ClearChat)
    bpy.utils.register_class(GPT4_OT_ShowCode)
    bpy.utils.register_class(GPT4_OT_DeleteMessage)
    bpy.utils.register_class(GPT4_OT_RefreshModels)
    bpy.utils.register_class(GPT4_OT_QuickAction)

    # Add to menu
    bpy.types.VIEW3D_MT_mesh_add.append(menu_func)

    # Initialize properties
    init_props()

    print("BlenderGPT v2.1.0 registered successfully!")


def unregister():
    # Clear properties first
    clear_props()

    # Remove from menu
    bpy.types.VIEW3D_MT_mesh_add.remove(menu_func)

    # Unregister all classes
    bpy.utils.unregister_class(GPT4AddonPreferences)
    bpy.utils.unregister_class(GPT4_OT_Execute)
    bpy.utils.unregister_class(GPT4_PT_Panel)
    bpy.utils.unregister_class(GPT4_OT_ClearChat)
    bpy.utils.unregister_class(GPT4_OT_ShowCode)
    bpy.utils.unregister_class(GPT4_OT_DeleteMessage)
    bpy.utils.unregister_class(GPT4_OT_RefreshModels)
    bpy.utils.unregister_class(GPT4_OT_QuickAction)

    print("BlenderGPT v2.1.0 unregistered successfully!")


if __name__ == "__main__":
    register()
