"""
Test script for BlenderGPT Security Module
Run this to test the security validation system
"""

import sys
import os

# Add current directory to path for imports
sys.path.append(os.path.dirname(__file__))

from security import CodeSecurityValidator, validate_and_preview_code

def test_security_validator():
    """Test the security validation system"""
    
    validator = CodeSecurityValidator()
    
    # Test cases
    test_cases = [
        # Safe code
        {
            "name": "Safe Blender Code",
            "code": """
import bpy
import bmesh

# Create a cube
bpy.ops.mesh.primitive_cube_add(location=(0, 0, 0))
cube = bpy.context.active_object
cube.name = "TestCube"

# Apply material
material = bpy.data.materials.new(name="TestMaterial")
material.diffuse_color = (1, 0, 0, 1)  # Red
cube.data.materials.append(material)
""",
            "should_pass": True
        },
        
        # Dangerous imports
        {
            "name": "Dangerous Import - os",
            "code": """
import os
import bpy

# This should be blocked
os.system("rm -rf /")
""",
            "should_pass": False
        },
        
        # Dangerous function calls
        {
            "name": "Dangerous Function - exec",
            "code": """
import bpy

# This should be blocked
exec("print('dangerous code')")
""",
            "should_pass": False
        },
        
        # File operations
        {
            "name": "File Operation",
            "code": """
import bpy

# This should generate a warning
with open("/tmp/test.txt", "w") as f:
    f.write("test")
""",
            "should_pass": True  # Warning, but not blocked
        },
        
        # Attribute access
        {
            "name": "Dangerous Attribute Access",
            "code": """
import bpy

# This should be blocked
obj = bpy.context.active_object
obj.__class__.__bases__
""",
            "should_pass": False
        },
        
        # Complex safe code
        {
            "name": "Complex Safe Code",
            "code": """
import bpy
import bmesh
import mathutils
import random

# Create multiple objects
for i in range(5):
    location = (random.uniform(-5, 5), random.uniform(-5, 5), 0)
    bpy.ops.mesh.primitive_cube_add(location=location)
    
    # Get the created object
    obj = bpy.context.active_object
    obj.name = f"Cube_{i}"
    
    # Random scale
    scale = random.uniform(0.5, 2.0)
    obj.scale = (scale, scale, scale)
    
    # Random rotation
    obj.rotation_euler = (
        random.uniform(0, 3.14159),
        random.uniform(0, 3.14159),
        random.uniform(0, 3.14159)
    )

# Create a material
material = bpy.data.materials.new(name="RandomMaterial")
material.use_nodes = True
material.node_tree.nodes.clear()

# Add principled BSDF
bsdf = material.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
material_output = material.node_tree.nodes.new(type='ShaderNodeOutputMaterial')
material.node_tree.links.new(bsdf.outputs['BSDF'], material_output.inputs['Surface'])

# Random color
bsdf.inputs['Base Color'].default_value = (
    random.random(),
    random.random(),
    random.random(),
    1.0
)
""",
            "should_pass": True
        }
    ]
    
    print("🔒 Testing BlenderGPT Security Validator")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print("-" * 30)
        
        is_safe, warnings, errors = validator.validate_code(test_case['code'])
        
        print(f"Code Safety: {'✅ SAFE' if is_safe else '❌ UNSAFE'}")
        
        if warnings:
            print("⚠️  Warnings:")
            for warning in warnings:
                print(f"   • {warning}")
        
        if errors:
            print("🚫 Errors:")
            for error in errors:
                print(f"   • {error}")
        
        # Check if result matches expectation
        if is_safe == test_case['should_pass']:
            print("✅ Test PASSED")
            passed += 1
        else:
            print("❌ Test FAILED")
            print(f"   Expected: {'SAFE' if test_case['should_pass'] else 'UNSAFE'}")
            print(f"   Got: {'SAFE' if is_safe else 'UNSAFE'}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Security system is working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the security implementation.")
    
    return failed == 0

def test_code_patterns():
    """Test specific dangerous code patterns"""
    
    print("\n🔍 Testing Dangerous Code Patterns")
    print("=" * 50)
    
    dangerous_patterns = [
        "rm -rf /",
        "del /s /q",
        "format(",
        "eval('malicious code')",
        "__import__('os')",
        "subprocess.call",
        "open('/etc/passwd')",
    ]
    
    validator = CodeSecurityValidator()
    
    for pattern in dangerous_patterns:
        test_code = f"""
import bpy
# Dangerous pattern test
{pattern}
"""
        
        is_safe, warnings, errors = validator.validate_code(test_code)
        
        if not is_safe or warnings:
            print(f"✅ Pattern '{pattern}' correctly detected as dangerous")
        else:
            print(f"❌ Pattern '{pattern}' was NOT detected (potential security hole)")

def main():
    """Run all security tests"""
    print("🚀 BlenderGPT Security Test Suite")
    print("=" * 60)
    
    # Test main validator
    validator_passed = test_security_validator()
    
    # Test pattern detection
    test_code_patterns()
    
    print("\n" + "=" * 60)
    if validator_passed:
        print("🎉 Security system is ready for production!")
    else:
        print("⚠️  Security system needs attention before deployment.")
    
    print("\n💡 Tips:")
    print("• Always enable security validation in production")
    print("• Review generated code before execution")
    print("• Keep the security module updated")
    print("• Report any security issues to the development team")

if __name__ == "__main__":
    main()
